<div class="card">
    <div class="card-stamp">
        <div class="card-stamp-icon bg-white text-primary"><i class="ti ti-users"></i></div>
    </div>
    <div class="card-body p-2">
        <div class="container-fluid">
            <form action="/admiz/system/mgmt/roles" class="row m-1" id="filterForm" method="get">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text">搜索</span>
                        <input autocomplete="off" class="form-control" name="searchText"
                               placeholder="角色名称、显示名称或备注"
                               type="text" value="{{ roleIndexVo.searchText }}">
                    </div>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text">状态</span>
                        <select class="form-select" name="disabled">
                            <option value="">全部</option>
                            <option value="false" {% if roleIndexVo.disabled == "false" %}selected{% endif %}>启用</option>
                            <option value="true" {% if roleIndexVo.disabled == "true" %}selected{% endif %}>禁用</option>
                        </select>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text">排序</span>
                        <select class="form-select" name="sort">
                            <option value="">默认排序</option>
                            <option value="name,asc" {% if roleIndexVo.sort == "name,asc" %}selected{% endif %}>角色名称 ↑</option>
                            <option value="name,desc" {% if roleIndexVo.sort == "name,desc" %}selected{% endif %}>角色名称 ↓</option>
                            <option value="sortNum,asc" {% if roleIndexVo.sort == "sortNum,asc" %}selected{% endif %}>排序号 ↑</option>
                            <option value="sortNum,desc" {% if roleIndexVo.sort == "sortNum,desc" %}selected{% endif %}>排序号 ↓</option>
                            <option value="createTime,asc" {% if roleIndexVo.sort == "createTime,asc" %}selected{% endif %}>创建时间 ↑</option>
                            <option value="createTime,desc" {% if roleIndexVo.sort == "createTime,desc" %}selected{% endif %}>创建时间 ↓</option>
                        </select>
                    </div>
                </div>
                <div class="col-auto">
                    <button class="btn btn-primary btn-4" type="submit">
                        <i class="ti ti-search"></i> 搜索
                    </button>
                </div>
                <input id="pageIndex" name="page" type="hidden" value="{{ page.getNumber() }}"/>
                <input id="pageSize" name="size" type="hidden" value="{{ page.getSize() }}"/>
            </form>
        </div>
    </div>
</div>
