{% extends "/layout/layout-content-embed.peb.html" %}
{% set title = "用户管理"|trans %}

{% block block_body %}
{% include "./_components.peb.html" %}
{% include "./_filter_box.peb.html" %}

<div class="card mt-2 mb-2">
    <div class="card-table">
        <div class="card-header p-2">
            <div class="row w-full">
                <div class="col">
                    <h3 class="card-title mb-0">用户管理</h3>
                    <p class="text-secondary m-0">管理系统用户</p>
                </div>
                <div class="col-md-auto col-sm-12">
                    <div class="ms-auto d-flex flex-wrap btn-list">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                data-bs-target="#userDetailsDialog">
                            Launch demo modal
                        </button>
                        <div class="btn btn-0 btn-outline-warning" id="btn-create-user"> 创建用户</div>
                        <div class="dropdown">
                            <div class="btn dropdown-toggle" data-bs-toggle="dropdown">批量操作</div>
                            <div class="dropdown-menu">
                                <a href="#" class="dropdown-item" id="btn-batch-assign-roles">批量分配角色</a>
                                <div class="dropdown-item">封禁</div>
                                <div class="dropdown-item">解封</div>
                                <div class="dropdown-item">导出</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="advanced-table">
            <div class="table-responsive">
                {% include "./_main.peb.html" %}
            </div>

            {{ paginator(page) }}
        </div>
    </div>
</div>

<!-- 批量角色管理模态框 -->
<div class="modal modal-blur fade" id="batchRoleAssignModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量角色管理</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 操作模式选择 -->
                <div class="mb-4">
                    <label class="form-label">操作模式</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="operation-mode" id="mode-add" value="add" checked>
                        <label class="btn btn-outline-success" for="mode-add">添加角色</label>

                        <input type="radio" class="btn-check" name="operation-mode" id="mode-remove" value="remove">
                        <label class="btn btn-outline-danger" for="mode-remove">移除角色</label>

                        <input type="radio" class="btn-check" name="operation-mode" id="mode-replace" value="replace">
                        <label class="btn btn-outline-primary" for="mode-replace">设置角色</label>
                    </div>
                    <small class="form-hint">
                        <strong>添加角色</strong>：在用户现有角色基础上添加新角色<br>
                        <strong>移除角色</strong>：从用户现有角色中移除指定角色<br>
                        <strong>设置角色</strong>：完全替换用户的角色为选中的角色
                    </small>
                </div>

                <div class="row">
                    <!-- 左侧：角色选择区域 -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">选择角色</label>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-secondary" id="btn-select-all-roles">
                                        全选
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="btn-clear-all-roles">
                                        清空
                                    </button>
                                </div>
                            </div>
                            <div id="role-selection-area" class="border rounded p-3"
                                 style="max-height: 400px; overflow-y: auto;">
                                <!-- 角色选择区域 -->
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：用户状态显示 -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">选中用户及其当前角色</label>
                            <div id="users-roles-display" class="border rounded p-3"
                                 style="max-height: 400px; overflow-y: auto;">
                                <!-- 用户角色状态显示区域 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作预览 -->
                <div class="mb-3">
                    <label class="form-label">操作预览</label>
                    <div id="operation-preview" class="alert alert-info">
                        请选择操作模式和角色
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btn-confirm-batch-operation">确认执行</button>
            </div>
        </div>
    </div>
</div>

<style>
    .hover-bg-light:hover {
        background-color: #f8f9fa !important;
        transition: background-color 0.2s ease;
    }

    .form-check:hover .form-check-input {
        border-color: #0d6efd;
    }

    .badge {
        font-size: 0.75em;
        padding: 0.35em 0.65em;
    }

    #role-selection-area, #users-roles-display {
        overflow-x: hidden;
    }

    #role-selection-area .d-flex {
        min-width: 0;
    }

    #role-selection-area .flex-grow-1 {
        min-width: 0;
        overflow: hidden;
    }

    .text-truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const batchAssignBtn = document.getElementById('btn-batch-assign-roles');
        const batchModalElement = document.getElementById('batchRoleAssignModal');
        const confirmBtn = document.getElementById('btn-confirm-batch-operation');

        let batchModal;
        if (typeof bootstrap !== 'undefined') {
            batchModal = new bootstrap.Modal(batchModalElement);
        } else if (typeof window.bootstrap !== 'undefined') {
            batchModal = new window.bootstrap.Modal(batchModalElement);
        } else {
            batchModal = {
                show: function () {
                    const backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    backdrop.id = 'modal-backdrop';
                    document.body.appendChild(backdrop);

                    batchModalElement.style.display = 'block';
                    batchModalElement.classList.add('show');
                    document.body.classList.add('modal-open');

                    backdrop.addEventListener('click', this.hide.bind(this));
                },
                hide: function () {
                    const backdrop = document.getElementById('modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }

                    batchModalElement.style.display = 'none';
                    batchModalElement.classList.remove('show');
                    document.body.classList.remove('modal-open');
                }
            };
        }

        let selectedUserIds = [];
        let availableRoles = [];
        let userRolesMap = new Map();

        // 模态框关闭按钮事件
        const closeButtons = batchModalElement.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', function () {
                if (batchModal && batchModal.hide) {
                    batchModal.hide();
                }
            });
        });

        // 操作模式切换事件
        const operationModes = document.querySelectorAll('input[name="operation-mode"]');
        operationModes.forEach(mode => {
            mode.addEventListener('change', updateOperationPreview);
        });

        // 角色选择变化事件
        document.addEventListener('change', function (e) {
            if (e.target.name === 'roleIds') {
                updateOperationPreview();
            }
        });

        // 全选/清空角色按钮
        document.getElementById('btn-select-all-roles').addEventListener('click', function () {
            const roleCheckboxes = document.querySelectorAll('input[name="roleIds"]');
            roleCheckboxes.forEach(cb => cb.checked = true);
            updateOperationPreview();
        });

        document.getElementById('btn-clear-all-roles').addEventListener('click', function () {
            const roleCheckboxes = document.querySelectorAll('input[name="roleIds"]');
            roleCheckboxes.forEach(cb => cb.checked = false);
            updateOperationPreview();
        });

        // 创建用户按钮点击事件
        const createUserBtn = document.getElementById('btn-create-user');
        if (createUserBtn) {
            createUserBtn.addEventListener('click', function (e) {
                e.preventDefault();
                window.location.href = '/admiz/system/mgmt/users/create';
            });
        }

        // 用户详情页点击事件
        document.addEventListener('click', function (e) {
            if (e.target.closest('.btn-edit-user')) {
                e.preventDefault();
                const userLink = e.target.closest('.btn-edit-user');
                const userId = userLink.getAttribute('data-user-id');

                if (userId && window.$ && window.$.openGlobalIFrameModal) {
                    const refreshOptions = {
                        enabled: true,
                        type: 'iframe',
                        selector: 'iframe[src*="/system/mgmt/users"]',
                        delay: 0
                    };
                    const modal = window.$.openGlobalIFrameModal(`/admiz/system/mgmt/users/details/account?userId=${userId}`, {}, null, refreshOptions);
                    modal.modal('show');

                    modal.on('hidden.bs.modal', function () {
                        if (window.top && window.top.document) {
                            const topBackdrops = window.top.document.querySelectorAll('.modal-backdrop');
                            topBackdrops.forEach(backdrop => backdrop.remove());
                            window.top.document.body.classList.remove('modal-open');
                            window.top.document.body.style.paddingRight = '';
                            window.top.document.body.style.overflow = '';
                        }

                        const currentBackdrops = document.querySelectorAll('.modal-backdrop');
                        currentBackdrops.forEach(backdrop => backdrop.remove());
                        document.body.classList.remove('modal-open');
                        document.body.style.paddingRight = '';
                        document.body.style.overflow = '';
                    });
                } else {
                    window.location.href = `/admiz/system/mgmt/users/details/account?userId=${userId}`;
                }
            }
        });

        // 批量分配角色按钮点击事件
        batchAssignBtn.addEventListener('click', function (e) {
            e.preventDefault();
            console.log('批量角色管理按钮被点击');

            const checkedUsers = document.querySelectorAll('input[name="userIds"]:checked');
            console.log('选中的用户数量:', checkedUsers.length);

            if (checkedUsers.length === 0) {
                alert('请先选择要操作的用户');
                return;
            }

            selectedUserIds = Array.from(checkedUsers).map(cb => parseInt(cb.value));
            console.log('选中的用户IDs:', selectedUserIds);

            Promise.all([
                loadUsersRolesInfo(selectedUserIds),
                loadAvailableRoles()
            ]).then(() => {
                displayUsersRolesStatus();
                renderRoleSelection();
                updateOperationPreview();
                batchModal.show();
            }).catch(error => {
                console.error('加载数据失败:', error);
                alert('加载数据失败，请重试');
            });
        });

        const newConfirmBtn = document.getElementById('btn-confirm-batch-operation');
        newConfirmBtn.addEventListener('click', function () {
            const selectedRoleIds = Array.from(document.querySelectorAll('input[name="roleIds"]:checked'))
                .map(cb => parseInt(cb.value));

            const operationMode = document.querySelector('input[name="operation-mode"]:checked').value;

            if (selectedRoleIds.length === 0) {
                alert('请选择要操作的角色');
                return;
            }

            const operationText = {
                'add': '添加角色',
                'remove': '移除角色',
                'replace': '设置角色'
            }[operationMode];

            if (!confirm(`确定要对 ${selectedUserIds.length} 个用户执行"${operationText}"操作吗？`)) {
                return;
            }

            const data = {
                userIds: selectedUserIds,
                roleIds: selectedRoleIds
            };

            let apiUrl;
            switch (operationMode) {
                case 'add':
                    apiUrl = '/admiz/api/system/mgmt/users/batch-add-roles';
                    break;
                case 'remove':
                    apiUrl = '/admiz/api/system/mgmt/users/batch-remove-roles';
                    break;
                case 'replace':
                    apiUrl = '/admiz/api/system/mgmt/users/batch-assign-roles';
                    break;
                default:
                    alert('无效的操作模式');
                    return;
            }

            newConfirmBtn.disabled = true;
            newConfirmBtn.textContent = '执行中...';

            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(result => {
                    console.log('Batch API Response:', result);
                    if (result.succeed || result.success) {
                        // alert(result.msg || `批量${operationText}成功`);
                        batchModal.hide();
                        window.location.reload();
                    } else {
                        alert(result.msg || result.message || `批量${operationText}失败`);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请重试');
                })
                .finally(() => {
                    newConfirmBtn.disabled = false;
                    newConfirmBtn.textContent = '确认执行';
                });
        });

        function loadUsersRolesInfo(userIds) {
            const params = new URLSearchParams();
            userIds.forEach(id => params.append('userIds', id));

            return fetch(`/admiz/api/system/mgmt/users/batch-roles-info?${params}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(result => {
                    console.log('Users Roles API Response:', result);
                    if ((result.succeed || result.success) && result.data) {
                        userRolesMap.clear();
                        Object.entries(result.data).forEach(([userId, roles]) => {
                            userRolesMap.set(parseInt(userId), roles);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading users roles:', error);
                    throw error;
                });
        }

        function loadAvailableRoles() {
            return fetch('/admiz/api/system/mgmt/roles?disabled=false')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(result => {
                    console.log('Roles API Response:', result);
                    if ((result.succeed || result.success) && result.data) {
                        availableRoles = result.data.content || result.data;
                    }
                })
                .catch(error => {
                    console.error('Error loading roles:', error);
                    throw error;
                });
        }

        function displayUsersRolesStatus() {
            const usersRolesDisplay = document.getElementById('users-roles-display');

            let html = '';
            selectedUserIds.forEach(userId => {
                const userRow = document.querySelector(`tr[data-user-id="${userId}"]`);
                if (userRow) {
                    const login = userRow.querySelector('.user-login').textContent;
                    const displayName = userRow.querySelector('.user-display-name').textContent;
                    const userRoles = userRolesMap.get(userId) || [];

                    html += `
                    <div class="mb-3 p-3 border rounded bg-light">
                        <div class="fw-bold text-primary mb-2">${displayName} (${login})</div>
                        <div>
                            <small class="text-muted d-block mb-1">当前角色：</small>
                            <div>
                                ${userRoles.length > 0 ?
                        userRoles.map(role => {
                            const displayName = role.displayName.length > 15
                                ? role.displayName.substring(0, 15) + '...'
                                : role.displayName;
                            return `<span class="badge bg-blue-lt text-blue me-1 mb-1" title="${role.displayName}">${displayName}</span>`;
                        }).join('') :
                        '<span class="text-muted fst-italic">无角色</span>'
                    }
                            </div>
                        </div>
                    </div>
                `;
                }
            });

            usersRolesDisplay.innerHTML = html;
        }

        // 渲染角色选择区域
        function renderRoleSelection() {
            const roleSelectionArea = document.getElementById('role-selection-area');

            let html = '';
            availableRoles.forEach(role => {
                // 限制备注长度为50字符
                const truncatedRemark = role.remark && role.remark.length > 50
                    ? role.remark.substring(0, 50) + '...'
                    : role.remark;

                html += `
                <div class="mb-2 p-2 border rounded hover-bg-light d-flex align-items-start">
                    <input class="form-check-input mt-1 me-2 flex-shrink-0" type="checkbox" name="roleIds" value="${role.id}" id="role-${role.id}">
                    <label class="flex-grow-1" for="role-${role.id}" style="cursor: pointer; min-width: 0;">
                        <div class="fw-bold text-dark text-truncate">${role.displayName}</div>
                        <small class="text-muted d-block text-truncate">${role.name}</small>
                        ${truncatedRemark ? `<small class="text-muted d-block fst-italic text-truncate" title="${role.remark || ''}">${truncatedRemark}</small>` : ''}
                    </label>
                </div>
            `;
            });

            roleSelectionArea.innerHTML = html;

            const roleCheckboxes = roleSelectionArea.querySelectorAll('input[name="roleIds"]');
            roleCheckboxes.forEach(cb => {
                cb.addEventListener('change', updateOperationPreview);
            });
        }

        function updateOperationPreview() {
            const operationMode = document.querySelector('input[name="operation-mode"]:checked')?.value;
            const selectedRoleIds = Array.from(document.querySelectorAll('input[name="roleIds"]:checked'))
                .map(cb => parseInt(cb.value));
            const selectedRoles = availableRoles.filter(role => selectedRoleIds.includes(role.id));

            const previewArea = document.getElementById('operation-preview');

            if (!operationMode || selectedRoles.length === 0) {
                previewArea.innerHTML = '请选择操作模式和角色';
                previewArea.className = 'alert alert-info';
                return;
            }

            let previewText = '';
            let alertClass = 'alert alert-info';

            switch (operationMode) {
                case 'add':
                    previewText = `将为 ${selectedUserIds.length} 个用户添加以下角色：<br>`;
                    previewText += selectedRoles.map(role => `<span class="badge bg-green-lt text-green me-1 mb-1">${role.displayName}</span>`).join('');
                    alertClass = 'alert alert-success';
                    break;
                case 'remove':
                    previewText = `将从 ${selectedUserIds.length} 个用户中移除以下角色：<br>`;
                    previewText += selectedRoles.map(role => `<span class="badge bg-red-lt text-red me-1 mb-1">${role.displayName}</span>`).join('');
                    alertClass = 'alert alert-warning';
                    break;
                case 'replace':
                    previewText = `将为 ${selectedUserIds.length} 个用户设置以下角色（完全替换现有角色）：<br>`;
                    previewText += selectedRoles.map(role => `<span class="badge bg-blue-lt text-blue me-1 mb-1">${role.displayName}</span>`).join('');
                    alertClass = 'alert alert-primary';
                    break;
            }

            previewArea.innerHTML = previewText;
            previewArea.className = alertClass;
        }
    });
</script>

{% endblock %}