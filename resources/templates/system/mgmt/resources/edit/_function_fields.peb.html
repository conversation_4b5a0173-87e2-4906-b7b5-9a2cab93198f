{# 功能类型资源专用字段 #}

<div class="alert alert-info">
    <div class="d-flex">
        <div>
            <i class="ti ti-tool"></i>
        </div>
        <div class="ms-2">
            <h4 class="alert-title">功能资源配置</h4>
            <div class="text-secondary">功能资源用于控制具体的操作权限，如增删改查等。</div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <label for="functionAction" class="form-label">功能操作 <span class="text-danger">*</span></label>
        <select class="form-select" id="functionAction" name="action" required>
            <option value="">请选择操作类型</option>
            <option value="create" {% if resource.action == 'create' %}selected{% endif %}>创建</option>
            <option value="read" {% if resource.action == 'read' %}selected{% endif %}>查看</option>
            <option value="update" {% if resource.action == 'update' %}selected{% endif %}>更新</option>
            <option value="delete" {% if resource.action == 'delete' %}selected{% endif %}>删除</option>
            <option value="export" {% if resource.action == 'export' %}selected{% endif %}>导出</option>
            <option value="import" {% if resource.action == 'import' %}selected{% endif %}>导入</option>
            <option value="approve" {% if resource.action == 'approve' %}selected{% endif %}>审批</option>
            <option value="custom" {% if resource.action == 'custom' %}selected{% endif %}>自定义</option>
        </select>
        <div class="form-text">定义此功能的具体操作类型</div>
    </div>
    <div class="col-md-6">
        <label for="functionMethod" class="form-label">HTTP方法</label>
        <select class="form-select" id="functionMethod" name="httpMethod">
            <option value="" {% if not resource.httpMethod %}selected{% endif %}>不限制</option>
            <option value="GET" {% if resource.httpMethod == 'GET' %}selected{% endif %}>GET</option>
            <option value="POST" {% if resource.httpMethod == 'POST' %}selected{% endif %}>POST</option>
            <option value="PUT" {% if resource.httpMethod == 'PUT' %}selected{% endif %}>PUT</option>
            <option value="DELETE" {% if resource.httpMethod == 'DELETE' %}selected{% endif %}>DELETE</option>
            <option value="PATCH" {% if resource.httpMethod == 'PATCH' %}selected{% endif %}>PATCH</option>
        </select>
        <div class="form-text">限制此功能允许的HTTP请求方法</div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <label for="functionUrl" class="form-label">API端点</label>
        <input type="text" class="form-control" id="functionUrl" name="url" 
               value="{{ resource.url | default('') }}" 
               placeholder="/api/system/users">
        <div class="form-text">此功能对应的API接口地址</div>
    </div>
    <div class="col-md-6">
        <label for="functionIcon" class="form-label">功能图标</label>
        <div class="input-group">
            <input type="text" class="form-control" id="functionIcon" name="icon" 
                   value="{{ resource.icon | default('ti ti-tool') }}" 
                   placeholder="ti ti-tool">
            <button class="btn btn-outline-secondary" type="button" id="functionIconPreview">
                <i class="{{ resource.icon | default('ti ti-tool') }}"></i>
            </button>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">权限控制</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-check">
                            <input class="form-check-input" type="checkbox" id="functionRequireAuth" checked>
                            <span class="form-check-label">需要认证</span>
                        </label>
                        <div class="form-text">执行此功能需要用户登录</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-check">
                            <input class="form-check-input" type="checkbox" id="functionAuditLog">
                            <span class="form-check-label">记录审计日志</span>
                        </label>
                        <div class="form-text">记录此功能的执行日志</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-check">
                            <input class="form-check-input" type="checkbox" id="functionRateLimit">
                            <span class="form-check-label">频率限制</span>
                        </label>
                        <div class="form-text">限制此功能的调用频率</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-3" id="rateLimitConfig" style="display: none;">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">频率限制配置</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="rateLimit" class="form-label">限制次数</label>
                        <input type="number" class="form-control" id="rateLimit" name="rateLimit" 
                               value="100" min="1">
                        <div class="form-text">时间窗口内允许的最大调用次数</div>
                    </div>
                    <div class="col-md-4">
                        <label for="rateLimitWindow" class="form-label">时间窗口（秒）</label>
                        <input type="number" class="form-control" id="rateLimitWindow" name="rateLimitWindow" 
                               value="60" min="1">
                        <div class="form-text">频率限制的时间窗口</div>
                    </div>
                    <div class="col-md-4">
                        <label for="rateLimitScope" class="form-label">限制范围</label>
                        <select class="form-select" id="rateLimitScope" name="rateLimitScope">
                            <option value="user">按用户</option>
                            <option value="ip">按IP</option>
                            <option value="global">全局</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 图标预览功能
    const iconInput = document.getElementById('functionIcon');
    const iconPreview = document.getElementById('functionIconPreview');
    
    if (iconInput && iconPreview) {
        iconInput.addEventListener('input', function() {
            const iconClass = this.value || 'ti ti-tool';
            iconPreview.innerHTML = `<i class="${iconClass}"></i>`;
        });
    }
    
    // 频率限制配置显示/隐藏
    const rateLimitCheckbox = document.getElementById('functionRateLimit');
    const rateLimitConfig = document.getElementById('rateLimitConfig');
    
    if (rateLimitCheckbox && rateLimitConfig) {
        rateLimitCheckbox.addEventListener('change', function() {
            rateLimitConfig.style.display = this.checked ? 'block' : 'none';
        });
    }
    
    // 根据操作类型自动设置HTTP方法
    const actionSelect = document.getElementById('functionAction');
    const methodSelect = document.getElementById('functionMethod');
    
    if (actionSelect && methodSelect) {
        actionSelect.addEventListener('change', function() {
            const action = this.value;
            const methodMap = {
                'create': 'POST',
                'read': 'GET',
                'update': 'PUT',
                'delete': 'DELETE',
                'export': 'GET',
                'import': 'POST',
                'approve': 'PUT'
            };
            
            if (methodMap[action]) {
                methodSelect.value = methodMap[action];
            }
        });
    }
});
</script>
