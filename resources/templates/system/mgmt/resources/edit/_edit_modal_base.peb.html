{# 资源编辑模态框基础模板 #}

<div class="modal fade" id="resourceEditModal" tabindex="-1" aria-labelledby="resourceEditModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="max-height: 90vh; overflow-y: auto;">
            <div class="modal-header py-2">
                <h6 class="modal-title mb-0" id="resourceEditModalLabel">
                    <i class="ti ti-edit me-2"></i>
                    <span id="modalTitle">编辑资源</span>
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body py-2">
                <form id="resourceEditForm" novalidate>
                    <input type="hidden" id="resourceId" name="id">

                    {# 基础字段 #}
                    <div class="card mb-2">
                        <div class="card-header py-1">
                            <h6 class="card-title mb-0">基本信息</h6>
                        </div>
                        <div class="card-body py-2">
                            <div class="row mb-2">
                                <div class="col-md-4">
                                    <label for="editResourceType" class="form-label">类型 <span class="text-danger">*</span></label>
                                    <select class="form-select form-select-sm" id="editResourceType" name="resourceType" required>
                                        <option value="">选择类型</option>
                                        <option value="FOLDER">目录</option>
                                        <option value="MENU">菜单</option>
                                        <option value="FUNCTION">功能</option>
                                        <option value="DATA">数据权限</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="editResourceParentId" class="form-label">上级资源</label>
                                    <select class="form-select form-select-sm" id="editResourceParentId" name="parentId">
                                        <option value="">无上级</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="editResourceSortNum" class="form-label">排序</label>
                                    <input type="number" class="form-control form-control-sm" id="editResourceSortNum" name="sortNum" value="100">
                                </div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="editResourceDisplayName" class="form-label">显示名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control form-control-sm" id="editResourceDisplayName" name="displayName" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="editResourcePermission" class="form-label">权限编码 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control form-control-sm" id="editResourcePermission" name="permission" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="editResourceName" class="form-label">资源名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control form-control-sm" id="editResourceName" name="name" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex gap-3 mt-4">
                                        <label class="form-check">
                                            <input class="form-check-input" id="editResourceDisabled" name="disabled" type="checkbox">
                                            <span class="form-check-label">禁用</span>
                                        </label>
                                        <label class="form-check">
                                            <input class="form-check-input" id="editResourceHidden" name="hidden" type="checkbox">
                                            <span class="form-check-label">隐藏</span>
                                        </label>
                                        <label class="form-check" id="autoRefreshOption" style="display: none;">
                                            <input class="form-check-input" id="editResourceAutoRefresh" name="autoRefresh" type="checkbox">
                                            <span class="form-check-label">自动刷新</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {# 类型特定字段容器 #}
                    <div id="typeSpecificFields">
                        {# 根据资源类型动态加载对应的字段模板 #}
                    </div>

                </form>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary btn-sm" id="saveResourceBtn">保存</button>
            </div>
        </div>
    </div>
</div>



<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('resourceEditModal');
    const form = document.getElementById('resourceEditForm');
    const typeSelect = document.getElementById('editResourceType');
    const typeSpecificFields = document.getElementById('typeSpecificFields');
    const saveBtn = document.getElementById('saveResourceBtn');
    
    let currentResourceType = '';
    let originalFormData = {};
    let typeSpecificFieldsData = {}; // 保存各类型的字段数据
    
    // 模态框显示时的初始化
    modal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const resourceId = button ? button.getAttribute('data-resource-id') : null;

        if (resourceId) {
            loadResourceData(resourceId);
        } else {
            // 新建资源
            resetForm();
            document.getElementById('modalTitle').textContent = '创建资源';
        }
    });
    
    // 资源类型变更处理
    if (typeSelect) {
        typeSelect.addEventListener('change', function() {
            const newType = this.value;

            // 保存当前类型的字段数据
            if (currentResourceType) {
                saveCurrentTypeFieldsData(currentResourceType);
            }

            currentResourceType = newType;
            loadTypeSpecificFields(newType);

            // 根据类型显示/隐藏"自动刷新"选项
            const autoRefreshOption = document.getElementById('autoRefreshOption');
            if (autoRefreshOption) {
                autoRefreshOption.style.display = newType === 'MENU' ? 'inline-block' : 'none';
            }
        });
    }
    
    // 保存按钮处理
    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            if (validateForm()) {
                saveResource();
            }
        });
    }
    
    // 加载资源数据
    function loadResourceData(resourceId) {
        // 先加载父级资源选项
        loadParentResourceOptions(resourceId, function() {
            // 然后加载资源详情
            fetch(`/admiz/api/system/mgmt/resources/${resourceId}`)
                .then(response => response.json())
                .then(result => {
                    if (result.succeed) {
                        const resource = result.data;
                        currentResourceType = resource.resourceType;
                        loadTypeSpecificFields(resource.resourceType);
                        // 延迟填充表单，确保类型特定字段已加载
                        setTimeout(() => {
                            populateForm(resource);
                        }, 100);
                        document.getElementById('modalTitle').textContent = `编辑资源 - ${resource.displayName || resource.name}`;
                    } else {
                        showToast('加载资源数据失败: ' + result.msg, 'danger');
                    }
                })
                .catch(error => {
                    showToast('加载资源数据失败', 'danger');
                    console.error('Error:', error);
                });
        });
    }

    // 加载父级资源选项
    function loadParentResourceOptions(excludeId, callback) {
        fetch('/admiz/api/system/mgmt/resources')
            .then(response => response.json())
            .then(result => {
                if (result.succeed) {
                    const parentSelect = document.getElementById('editResourceParentId');
                    parentSelect.innerHTML = '<option value="">无父级（顶级资源）</option>';

                    const parentResources = result.data.content.filter(r =>
                        r.id !== excludeId &&
                        (r.resourceType === 'FOLDER' || r.resourceType === 'MENU')
                    );

                    parentResources.forEach(resource => {
                        parentSelect.innerHTML += `<option value="${resource.id}">${resource.displayName || resource.name}</option>`;
                    });

                    if (callback) callback();
                } else {
                    showToast('加载父级资源失败: ' + result.msg, 'danger');
                }
            })
            .catch(error => {
                showToast('加载父级资源失败', 'danger');
                console.error('Error:', error);
            });
    }
    
    // 填充表单数据
    function populateForm(resource) {
        document.getElementById('resourceId').value = resource.id || '';
        document.getElementById('editResourceName').value = resource.name || '';
        document.getElementById('editResourceDisplayName').value = resource.displayName || '';
        document.getElementById('editResourcePermission').value = resource.permission || '';
        document.getElementById('editResourceType').value = resource.resourceType || '';
        document.getElementById('editResourceParentId').value = resource.parentId || '';
        document.getElementById('editResourceSortNum').value = resource.sortNum || 100;
        document.getElementById('editResourceDisabled').checked = resource.disabled || false;
        document.getElementById('editResourceHidden').checked = resource.hidden || false;
        document.getElementById('editResourceAutoRefresh').checked = resource.autoRefresh || false;
        document.getElementById('editResourceRemark').value = resource.remark || '';

        // 填充类型特定字段
        populateTypeSpecificFields(resource);

        // 保存该类型的字段数据
        saveTypeSpecificFieldsFromResource(resource);

        // 保存原始数据用于比较
        originalFormData = { ...resource };
    }

    // 填充类型特定字段数据
    function populateTypeSpecificFields(resource) {
        const resourceType = resource.resourceType;

        // 填充备注字段（所有类型都有）
        const remarkField = document.getElementById('editResourceRemark');
        if (remarkField) remarkField.value = resource.remark || '';

        switch(resourceType) {
            case 'FOLDER':
                const folderIcon = document.getElementById('folderIcon');
                if (folderIcon) folderIcon.value = resource.icon || 'ti ti-folder';
                break;

            case 'MENU':
                const menuUrl = document.getElementById('menuUrl');
                const menuTarget = document.getElementById('menuTarget');
                const menuIcon = document.getElementById('menuIcon');
                if (menuUrl) menuUrl.value = resource.url || '';
                if (menuTarget) menuTarget.value = resource.target || '';
                if (menuIcon) menuIcon.value = resource.icon || 'ti ti-menu-2';
                break;

            case 'FUNCTION':
                const functionUrl = document.getElementById('functionUrl');
                if (functionUrl) functionUrl.value = resource.url || '';
                break;

            case 'DATA':
                // 数据权限类型的备注已在上面处理
                break;
        }
    }

    // 从资源对象保存类型特定字段数据
    function saveTypeSpecificFieldsFromResource(resource) {
        const resourceType = resource.resourceType;
        if (!resourceType) return;

        const data = {};

        // 保存备注字段（所有类型都有）
        data.remark = resource.remark || '';

        switch(resourceType) {
            case 'FOLDER':
                data.icon = resource.icon || '';
                break;

            case 'MENU':
                data.url = resource.url || '';
                data.target = resource.target || '';
                data.icon = resource.icon || '';
                break;

            case 'FUNCTION':
                data.url = resource.url || '';
                break;

            case 'DATA':
                // 数据权限类型的备注已在上面处理
                break;
        }

        typeSpecificFieldsData[resourceType] = data;
    }
    
    // 保存当前类型的字段数据
    function saveCurrentTypeFieldsData(resourceType) {
        if (!resourceType) return;

        const data = {};

        // 保存备注字段（所有类型都有）
        const remarkField = document.getElementById('editResourceRemark');
        if (remarkField) data.remark = remarkField.value;

        switch(resourceType) {
            case 'FOLDER':
                const folderIcon = document.getElementById('folderIcon');
                if (folderIcon) data.icon = folderIcon.value;
                break;

            case 'MENU':
                const menuUrl = document.getElementById('menuUrl');
                const menuTarget = document.getElementById('menuTarget');
                const menuIcon = document.getElementById('menuIcon');
                if (menuUrl) data.url = menuUrl.value;
                if (menuTarget) data.target = menuTarget.value;
                if (menuIcon) data.icon = menuIcon.value;
                break;

            case 'FUNCTION':
                const functionUrl = document.getElementById('functionUrl');
                if (functionUrl) data.url = functionUrl.value;
                break;

            case 'DATA':
                // 数据权限类型的备注已在上面处理
                break;
        }

        typeSpecificFieldsData[resourceType] = data;
    }

    // 恢复类型字段数据
    function restoreTypeFieldsData(resourceType) {
        if (!resourceType || !typeSpecificFieldsData[resourceType]) return;

        const data = typeSpecificFieldsData[resourceType];

        // 延迟执行，确保DOM元素已创建
        setTimeout(() => {
            // 恢复备注字段（所有类型都有）
            const remarkField = document.getElementById('editResourceRemark');
            if (remarkField && data.remark !== undefined) remarkField.value = data.remark;

            switch(resourceType) {
                case 'FOLDER':
                    const folderIcon = document.getElementById('folderIcon');
                    if (folderIcon && data.icon !== undefined) folderIcon.value = data.icon;
                    break;

                case 'MENU':
                    const menuUrl = document.getElementById('menuUrl');
                    const menuTarget = document.getElementById('menuTarget');
                    const menuIcon = document.getElementById('menuIcon');
                    if (menuUrl && data.url !== undefined) menuUrl.value = data.url;
                    if (menuTarget && data.target !== undefined) menuTarget.value = data.target;
                    if (menuIcon && data.icon !== undefined) menuIcon.value = data.icon;
                    break;

                case 'FUNCTION':
                    const functionUrl = document.getElementById('functionUrl');
                    if (functionUrl && data.url !== undefined) functionUrl.value = data.url;
                    break;

                case 'DATA':
                    // 数据权限类型的备注已在上面处理
                    break;
            }
        }, 50);
    }

    // 重置表单
    function resetForm() {
        form.reset();
        typeSpecificFields.innerHTML = '';
        currentResourceType = '';
        originalFormData = {};
        typeSpecificFieldsData = {}; // 清空保存的字段数据

        // 加载父级资源选项
        loadParentResourceOptions(null);
    }
    
    // 加载类型特定字段
    function loadTypeSpecificFields(resourceType) {
        if (!resourceType) {
            typeSpecificFields.innerHTML = '';
            return;
        }

        // 直接根据类型显示对应的字段内容
        let fieldsHtml = '';

        switch(resourceType) {
            case 'FOLDER':
                fieldsHtml = getFolderFields();
                break;
            case 'MENU':
                fieldsHtml = getMenuFields();
                break;
            case 'FUNCTION':
                fieldsHtml = getFunctionFields();
                break;
            case 'DATA':
                fieldsHtml = getDataFields();
                break;
        }

        typeSpecificFields.innerHTML = fieldsHtml;

        // 初始化字段事件
        initTypeSpecificEvents(resourceType);

        // 恢复该类型之前保存的数据
        restoreTypeFieldsData(resourceType);
    }

    // 获取目录字段HTML
    function getFolderFields() {
        return `
            <div class="card mb-2">
                <div class="card-header py-1">
                    <h6 class="card-title mb-0"><i class="ti ti-folder text-warning me-2"></i>目录配置</h6>
                </div>
                <div class="card-body py-2">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="folderIcon" class="form-label">图标</label>
                            <input type="text" class="form-control form-control-sm" id="folderIcon" name="icon"
                                   placeholder="ti ti-folder">
                        </div>
                        <div class="col-md-6">
                            <label for="editResourceRemark" class="form-label">备注</label>
                            <textarea class="form-control form-control-sm" id="editResourceRemark" name="remark" rows="2"
                                      placeholder="目录说明"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 获取菜单字段HTML
    function getMenuFields() {
        return `
            <div class="card mb-2">
                <div class="card-header py-1">
                    <h6 class="card-title mb-0"><i class="ti ti-menu-2 text-primary me-2"></i>菜单配置</h6>
                </div>
                <div class="card-body py-2">
                    <div class="row mb-2">
                        <div class="col-md-6">
                            <label for="menuUrl" class="form-label">访问地址 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-sm" id="menuUrl" name="url"
                                   placeholder="/system/user" required>
                        </div>
                        <div class="col-md-3">
                            <label for="menuTarget" class="form-label">打开方式</label>
                            <select class="form-select form-select-sm" id="menuTarget" name="target">
                                <option value="">当前窗口</option>
                                <option value="_blank">新窗口</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="menuIcon" class="form-label">图标</label>
                            <input type="text" class="form-control form-control-sm" id="menuIcon" name="icon"
                                   placeholder="ti ti-menu-2">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <label for="editResourceRemark" class="form-label">备注</label>
                            <textarea class="form-control form-control-sm" id="editResourceRemark" name="remark" rows="2"
                                      placeholder="菜单说明"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 获取功能字段HTML
    function getFunctionFields() {
        return `
            <div class="card mb-2">
                <div class="card-header py-1">
                    <h6 class="card-title mb-0"><i class="ti ti-tool text-info me-2"></i>功能配置</h6>
                </div>
                <div class="card-body py-2">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="functionUrl" class="form-label">关联接口</label>
                            <input type="text" class="form-control form-control-sm" id="functionUrl" name="url"
                                   placeholder="/api/system/user">
                        </div>
                        <div class="col-md-6">
                            <label for="editResourceRemark" class="form-label">备注</label>
                            <textarea class="form-control form-control-sm" id="editResourceRemark" name="remark" rows="2"
                                      placeholder="功能说明"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 获取数据权限字段HTML
    function getDataFields() {
        return `
            <div class="card mb-2">
                <div class="card-header py-1">
                    <h6 class="card-title mb-0"><i class="ti ti-database text-success me-2"></i>数据权限配置</h6>
                </div>
                <div class="card-body py-2">
                    <div class="row">
                        <div class="col-md-12">
                            <label for="editResourceRemark" class="form-label">权限说明</label>
                            <textarea class="form-control form-control-sm" id="editResourceRemark" name="remark" rows="2"
                                      placeholder="描述此数据权限的具体作用范围"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 初始化类型特定事件
    function initTypeSpecificEvents(resourceType) {
        switch(resourceType) {
            case 'FOLDER':
                initFolderEvents();
                break;
            case 'MENU':
                initMenuEvents();
                break;
            case 'FUNCTION':
                initFunctionEvents();
                break;
            case 'DATA':
                initDataEvents();
                break;
        }
    }

    function initFolderEvents() {
        // 目录类型特定事件处理（如果需要的话）
    }

    function initMenuEvents() {
        const urlInput = document.getElementById('menuUrl');
        if (urlInput) {
            urlInput.addEventListener('blur', function() {
                const url = this.value.trim();
                if (url && !url.startsWith('/')) {
                    this.value = '/' + url;
                }
            });
        }
    }

    function initFunctionEvents() {
        // 功能类型特定事件处理（如果需要的话）
    }

    function initDataEvents() {
        // 数据权限类型特定事件处理（如果需要的话）
    }

    // 表单验证
    function validateForm() {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        return isValid;
    }
    
    // 保存资源
    function saveResource() {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // 处理复选框
        data.disabled = document.getElementById('editResourceDisabled').checked;
        data.hidden = document.getElementById('editResourceHidden').checked;
        data.autoRefresh = document.getElementById('editResourceAutoRefresh').checked;
        
        const resourceId = data.id;
        const url = resourceId ? 
            `/admiz/api/system/mgmt/resources/${resourceId}` : 
            '/admiz/api/system/mgmt/resources';
        const method = resourceId ? 'PUT' : 'POST';
        
        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.succeed) {
                showToast(resourceId ? '更新成功' : '创建成功', 'success');
                $('#resourceEditModal').modal('hide');
                // 刷新页面或重新加载数据
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('保存失败: ' + result.msg, 'danger');
            }
        })
        .catch(error => {
            showToast('保存失败', 'danger');
            console.error('Error:', error);
        });
    }
    
    // 显示提示消息
    function showToast(message, type = 'success') {
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            alert(message);
        }
    }

    // 暴露为全局函数供其他脚本调用
    window.loadParentResourceOptions = loadParentResourceOptions;
});
</script>
