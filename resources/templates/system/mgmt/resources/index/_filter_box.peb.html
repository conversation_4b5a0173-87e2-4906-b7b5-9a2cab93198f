<div class="card">
    <div class="card-stamp">
        <div class="card-stamp-icon bg-white text-primary"><i class="ti ti-filter"></i></div>
    </div>
    <div class="card-body p-2">
        <div class="container-fluid">
            <div class="row m-1" id="filterForm">
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="ti ti-search"></i>
                        </span>
                        <input autocomplete="off" class="form-control" id="searchText"
                               placeholder="搜索资源名称、显示名称或权限编码..." type="text">
                        <button class="btn btn-outline-secondary" type="button" id="btn-clear-search"
                                title="清空搜索">
                            <i class="ti ti-x"></i>
                        </button>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text">类型</span>
                        <select class="form-select" id="resourceType">
                            <option value="">所有类型</option>
                            <option value="FOLDER">目录</option>
                            <option value="MENU">菜单</option>
                            <option value="FUNCTION">功能</option>
                            <option value="DATA">数据</option>
                        </select>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text">状态</span>
                        <select class="form-select" id="disabled">
                            <option value="">所有状态</option>
                            <option value="false">启用</option>
                            <option value="true">禁用</option>
                        </select>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button class="btn btn-primary" id="btn-search" type="button">
                            <i class="ti ti-search"></i> 搜索
                        </button>
                        <button class="btn btn-outline-secondary" id="btn-reset-filter" type="button" title="重置所有过滤条件">
                            <i class="ti ti-refresh"></i> 重置
                        </button>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="ti ti-adjustments"></i> 高级
                        </button>
                        <div class="dropdown-menu dropdown-menu-end p-3" style="min-width: 300px;">
                            <h6 class="dropdown-header">高级搜索选项</h6>
                            <div class="mb-3">
                                <label class="form-label">层级深度</label>
                                <select class="form-select form-select-sm" id="levelFilter">
                                    <option value="">所有层级</option>
                                    <option value="0">顶级资源</option>
                                    <option value="1">第二层</option>
                                    <option value="2">第三层</option>
                                    <option value="3">第四层及以下</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-check">
                                    <input class="form-check-input" type="checkbox" id="hasChildrenFilter">
                                    <span class="form-check-label">仅显示有子项的资源</span>
                                </label>
                            </div>
                            <div class="mb-3">
                                <label class="form-check">
                                    <input class="form-check-input" type="checkbox" id="hiddenFilter">
                                    <span class="form-check-label">包含隐藏资源</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(() => {
        // 搜索按钮
        $('#btn-search').click(() => {
            filterResourceTree();
        });

        // 重置过滤器
        $('#btn-reset-filter').click(() => {
            $('#searchText').val('');
            $('#resourceType').val('');
            $('#disabled').val('');
            $('#levelFilter').val('');
            $('#hasChildrenFilter').prop('checked', false);
            $('#hiddenFilter').prop('checked', false);
            filterResourceTree();
        });

        // 清空搜索
        $('#btn-clear-search').click(() => {
            $('#searchText').val('');
            filterResourceTree();
        });

        // 回车搜索
        $('#searchText').keypress(function (e) {
            if (e.which === 13) {
                filterResourceTree();
            }
        });

        // 过滤器变化时自动搜索
        $('#resourceType, #disabled, #levelFilter, #hasChildrenFilter, #hiddenFilter').change(() => {
            filterResourceTree();
        });

        // 过滤资源树
        function filterResourceTree() {
            const searchText = $('#searchText').val().trim();
            const resourceType = $('#resourceType').val();
            const disabled = $('#disabled').val();
            const levelFilter = $('#levelFilter').val();
            const hasChildrenFilter = $('#hasChildrenFilter').prop('checked');
            const hiddenFilter = $('#hiddenFilter').prop('checked');

            // 触发树状视图的过滤事件
            $(document).trigger('filterResourceTree', {
                searchText: searchText,
                resourceType: resourceType,
                disabled: disabled,
                levelFilter: levelFilter,
                hasChildrenFilter: hasChildrenFilter,
                hiddenFilter: hiddenFilter
            });
        }
    });
</script>
