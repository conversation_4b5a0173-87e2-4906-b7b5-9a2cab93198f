{# 资源管理相关的宏定义 #}

{# 资源类型图标宏 #}
{% macro resourceTypeIcon(resourceType) %}
    {% if resourceType == 'FOLDER' %}
        <i class="ti ti-folder text-warning"></i>
    {% elseif resourceType == 'MENU' %}
        <i class="ti ti-menu-2 text-primary"></i>
    {% elseif resourceType == 'FUNCTION' %}
        <i class="ti ti-tool text-info"></i>
    {% elseif resourceType == 'DATA' %}
        <i class="ti ti-database text-success"></i>
    {% else %}
        <i class="ti ti-file"></i>
    {% endif %}
{% endmacro %}

{# 资源类型文本宏 #}
{% macro resourceTypeText(resourceType) %}
    {% if resourceType == 'FOLDER' %}目录
    {% elseif resourceType == 'MENU' %}菜单
    {% elseif resourceType == 'FUNCTION' %}功能
    {% elseif resourceType == 'DATA' %}数据
    {% else %}{{ resourceType }}
    {% endif %}
{% endmacro %}

{# 资源状态徽章宏 #}
{% macro statusBadge(resource) %}
    {% if resource.disabled %}
        <span class="badge bg-red-lt text-red">已禁用</span>
    {% elseif resource.hidden %}
        <span class="badge bg-yellow-lt text-yellow">已隐藏</span>
    {% else %}
        <span class="badge bg-green-lt text-green">正常</span>
    {% endif %}
{% endmacro %}

{# 基础表单字段宏 #}
{% macro baseFormFields(resource, parentResources) %}
    <div class="row mb-3">
        <div class="col-md-6">
            <label for="resourceName" class="form-label">资源名称 <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="resourceName" name="name" 
                   value="{{ resource.name | default('') }}" required>
            <div class="invalid-feedback"></div>
        </div>
        <div class="col-md-6">
            <label for="resourceDisplayName" class="form-label">显示名称 <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="resourceDisplayName" name="displayName" 
                   value="{{ resource.displayName | default('') }}" required>
            <div class="invalid-feedback"></div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
            <label for="resourcePermission" class="form-label">权限编码 <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="resourcePermission" name="permission" 
                   value="{{ resource.permission | default('') }}" required>
            <div class="invalid-feedback"></div>
        </div>
        <div class="col-md-6">
            <label for="resourceType" class="form-label">资源类型 <span class="text-danger">*</span></label>
            <select class="form-select" id="resourceType" name="resourceType" required>
                <option value="">请选择资源类型</option>
                <option value="FOLDER" {% if resource.resourceType == 'FOLDER' %}selected{% endif %}>目录</option>
                <option value="MENU" {% if resource.resourceType == 'MENU' %}selected{% endif %}>菜单</option>
                <option value="FUNCTION" {% if resource.resourceType == 'FUNCTION' %}selected{% endif %}>功能</option>
                <option value="DATA" {% if resource.resourceType == 'DATA' %}selected{% endif %}>数据</option>
            </select>
            <div class="invalid-feedback"></div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
            <label for="resourceParentId" class="form-label">父级资源</label>
            <select class="form-select" id="resourceParentId" name="parentId">
                <option value="">无父级（顶级资源）</option>
                {% for parent in parentResources %}
                    <option value="{{ parent.id }}" 
                            {% if resource.parentId == parent.id %}selected{% endif %}>
                        {{ parent.displayName | default(parent.name) }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-6">
            <label for="resourceSortNum" class="form-label">排序</label>
            <input type="number" class="form-control" id="resourceSortNum" name="sortNum" 
                   value="{{ resource.sortNum | default(100) }}">
        </div>
    </div>
{% endmacro %}

{# 状态选项宏 #}
{% macro statusOptions(resource) %}
    <div class="row mb-3">
        <div class="col-md-12">
            <label class="form-label">状态选项</label>
            <div>
                <label class="form-check form-check-inline">
                    <input class="form-check-input" id="resourceDisabled" name="disabled" type="checkbox" 
                           {% if resource.disabled %}checked{% endif %}>
                    <span class="form-check-label">禁用</span>
                </label>
                <label class="form-check form-check-inline">
                    <input class="form-check-input" id="resourceHidden" name="hidden" type="checkbox" 
                           {% if resource.hidden %}checked{% endif %}>
                    <span class="form-check-label">隐藏</span>
                </label>
                <label class="form-check form-check-inline">
                    <input class="form-check-input" id="resourceAutoRefresh" name="autoRefresh" type="checkbox" 
                           {% if resource.autoRefresh %}checked{% endif %}>
                    <span class="form-check-label">自动刷新</span>
                </label>
            </div>
        </div>
    </div>
{% endmacro %}

{# 备注字段宏 #}
{% macro remarkField(resource) %}
    <div class="mb-3">
        <label for="resourceRemark" class="form-label">备注</label>
        <textarea class="form-control" id="resourceRemark" name="remark" rows="3" 
                  placeholder="资源描述或备注信息">{{ resource.remark | default('') }}</textarea>
    </div>
{% endmacro %}
